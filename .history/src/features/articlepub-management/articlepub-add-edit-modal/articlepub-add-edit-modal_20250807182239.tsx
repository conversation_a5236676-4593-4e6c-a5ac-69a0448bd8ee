import { useEffect, useRef } from 'react'
import { useRecoilState } from 'recoil'
import { useForm } from 'react-hook-form'
import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { articlepubDetailState, type ArticlepubDetailState } from '@/features/shared/states'
import {
    AppButton,
    AppModal,
    AppNormalInput,
    AppTiptapEditor,
    type AppTiptapEditorRef,
    RequiredIndicator,
} from '@/features/shared/ui'
import { usePermission, useToast } from '@/features/shared/utils'
import {
    ArticlepubServices,
} from '@/features/shared/services'


const ADD_PERMISSION = 2300100
const EDIT_PERMISSION = 2300300

interface FormValues {
    title: string
    content: string
}

const schema: yup.ObjectSchema<FormValues> = yup
    .object()
    .shape({
        title: yup.string().required('Message title is required').max(50, 'Message title must be less than 50 characters'),
        content: yup.string().required('Message content is required').max(100000, 'Message content must be less than 100000 characters'),
    })
    .required()

export function ArticlepubAddEditModal() {
    const editorRef = useRef<AppEditorJSRef>(null)
    const queryClient = useQueryClient()
    const { showToast } = useToast()
    const { hasPermission } = usePermission()
    const [{ mode, selectedArticlepub }, setArticlepubDetailState] =
        useRecoilState<ArticlepubDetailState>(articlepubDetailState)

    const { data: articlepubData } = useQuery({
        enabled: Boolean(selectedArticlepub?.id && mode === 'EDIT'),
        queryFn: () =>
            ArticlepubServices.getArticlepubDetail({
                id: Number(selectedArticlepub?.id),
            }),
        queryKey: ['message-detail-by-id', selectedArticlepub?.id],
    })

    const {
        formState: { isValid, errors },
        register,
        handleSubmit,
        reset,
        watch,
        setValue,
        trigger,
    } = useForm<FormValues>({
        defaultValues: {
            title: '',
            content: '',
        },
        mode: 'onChange',
        reValidateMode: 'onChange',
        resolver: yupResolver(schema),
    })

    useEffect(() => {
        if (articlepubData) {
            reset({
                title: articlepubData.title,
                content: articlepubData.content,
            })
        }
    }, [articlepubData, reset])

    const mutateCreateArticlepub = useMutation({
        mutationFn: ArticlepubServices.createArticlepub,
        onError: (error) => {
            showToast('Articlepub Creation Failed', error.message, 'danger')
        },
        onSuccess: () => {
            __onClose()
            queryClient.invalidateQueries({
                queryKey: ['message-center-list'],
            })
            showToast('Articlepub Created Successfully', 'success')
        },
    })

    const mutateUpdateArticlepub = useMutation({
        mutationFn: ArticlepubServices.updateArticlepub,
        onError: (error) => {
            showToast('Articlepub Update Failed', error.message, 'danger')
        },
        onSuccess: () => {
            __onClose()
            queryClient.invalidateQueries({
                queryKey: ['message-center-list'],
            })
            showToast('Articlepub Updated Successfully', 'success')
        },
    })

    const mutateCreateAndPublishArticlepub = useMutation({
        mutationFn: ArticlepubServices.createAndPublishArticlepub,
        onError: (error) => {
            showToast('Articlepub Creation and Publish Failed', error.message, 'danger')
        },
        onSuccess: () => {
            __onClose()
            queryClient.invalidateQueries({
                queryKey: ['message-center-list'],
            })
            showToast('Articlepub Created and Publish Successfully', 'success')
        },
    })

    const mutateUpdateAndPublishArticlepub = useMutation({
        mutationFn: ArticlepubServices.updateAndPublishArticlepub,
        onError: (error) => {
            showToast('Articlepub Update and Publish Failed', error.message, 'danger')
        },
        onSuccess: () => {
            __onClose()
            queryClient.invalidateQueries({
                queryKey: ['message-center-list'],
            })
            showToast('Message Updated and Publish Successfully', 'success')
        },
    })

    const __onClose = () => {
        reset({})
        setArticlepubDetailState((prev) => ({
            ...prev,
            mode: undefined,
            selectArticle: undefined,
        }))
    }

    const __onSave = handleSubmit((data: FormValues) => {
        if (isValid) {
            const payload = {
                title: data.title,
                content: btoa(unescape(encodeURIComponent(data.content))), // Browser-compatible base64 encoding
                action: 'SAVE' as const,
            }
    
            if (mode === 'ADD') {
                mutateCreateArticlepub.mutateAsync(payload)
            } else if (mode === 'EDIT' && selectedArticlepub?.id) {
                mutateUpdateArticlepub.mutateAsync({
                    id: selectedArticlepub.id,
                    title: payload.title,
                    content: payload.content,
                    action: payload.action,
                })
            }
        }
    })

    const __onSaveAndPublish = handleSubmit((data: FormValues) => {
        if (isValid) {
            const payload = {
                title: data.title,
                content: btoa(unescape(encodeURIComponent(data.content))), // Browser-compatible base64 encoding
                action: 'PUBLISH' as const,
            }
    
            if (mode === 'ADD') {
                mutateCreateAndPublishArticlepub.mutateAsync(payload)
            } else if (mode === 'EDIT' && selectedArticlepub?.id) {
                mutateUpdateAndPublishArticlepub.mutateAsync({
                    id: selectedArticlepub.id,
                    title: payload.title,
                    content: payload.content,
                    action: payload.action,
                })
            }
        }
    })

    return (
        <AppModal
            childrenWrapperClassName="pb-0"
            onCancel={__onClose}
            open
            titleLabel={mode === 'ADD' ? 'Add Article' : 'Edit Article'}
            width={800}
        >
            <form className="flex flex-col gap-3" onSubmit={__onSave}>
                <AppNormalInput
                    label="Article Title"
                    {...register('title')}
                    error={errors.title?.message}
                    placeholder="Enter article title"
                    required
                />
                
                <AppEditorJS
                    ref={editorRef}
                    label="Article Content"
                    value={watch('content')}
                    onChange={(value: string) => setValue('content', value)}
                    onBlur={() => trigger('content')}
                    error={errors.content?.message}
                    placeholder="Enter article content with rich formatting..."
                    required
                    maxLength={10000}
                />

                <RequiredIndicator />

                <section className="sticky bottom-0 left-0 right-0 grid grid-cols-3 gap-2 bg-white py-6">
                    <AppButton
                        cosmeticType="skeleton"
                        label="Cancel"
                        onClick={__onClose}
                    />

                    {mode === 'ADD' && hasPermission(ADD_PERMISSION) && (
                        <>
                            <AppButton
                                cosmeticType="tertiary"
                                disabled={!isValid}
                                isLoading={mutateCreateArticlepub.isPending}
                                label="Save Draft"
                                onClick={__onSave}
                                type="button"
                            />
                            <AppButton
                                cosmeticType="secondary"
                                disabled={!isValid}
                                isLoading={mutateCreateArticlepub.isPending}
                                label="Save & Publish"
                                onClick={__onSaveAndPublish}
                                type="button"
                            />
                        </>
                    )}

                    {mode === 'EDIT' && hasPermission(EDIT_PERMISSION) && (
                        <>
                            <AppButton
                                cosmeticType="tertiary"
                                disabled={!isValid}
                                isLoading={mutateUpdateArticlepub.isPending}
                                label="Save Draft"
                                onClick={__onSave}
                                type="button"
                            />
                            <AppButton
                                cosmeticType="secondary"
                                disabled={!isValid}
                                isLoading={mutateUpdateArticlepub.isPending}
                                label="Save & Publish"
                                onClick={__onSaveAndPublish}
                                type="button"
                            />
                        </>
                    )}
                </section>
            </form>
        </AppModal>
    )
}