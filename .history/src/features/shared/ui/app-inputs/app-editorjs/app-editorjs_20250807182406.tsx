import { forwardRef, useEffect, useImperativeHandle, useRef, useCallback, useState } from 'react'
import { useEditor, EditorContent, Editor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Underline from '@tiptap/extension-underline'
import Link from '@tiptap/extension-link'
import Image from '@tiptap/extension-image'
import { Table } from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableCell from '@tiptap/extension-table-cell'
import TableHeader from '@tiptap/extension-table-header'
import CharacterCount from '@tiptap/extension-character-count'
import Placeholder from '@tiptap/extension-placeholder'
import { TextStyle } from '@tiptap/extension-text-style'
import Color from '@tiptap/extension-color'
import Highlight from '@tiptap/extension-highlight'
import { ReactSVG } from 'react-svg'
import { formInvalidIcon } from '../../app-icons'
import './app-tiptap-editor.css'

interface AppTiptapEditorProps {
  label?: string
  value?: string
  onChange?: (value: string) => void
  onBlur?: () => void
  placeholder?: string
  error?: string
  wrapperClassName?: string
  required?: boolean
  disabled?: boolean
  maxLength?: number
}

export interface AppTiptapEditorRef {
  focus: () => void
  blur: () => void
  getEditor: () => Editor | null
}

function AppTiptapEditorInner(
  props: AppTiptapEditorProps,
  ref: React.ForwardedRef<AppTiptapEditorRef>
) {
  const {
    label,
    value = '',
    onChange,
    onBlur,
    placeholder = 'Enter article content...',
    error,
    wrapperClassName,
    required,
    disabled,
    maxLength = 10000
  } = props

  const [characterCount, setCharacterCount] = useState(0)

  // Initialize Tiptap editor
  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg',
        },
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      CharacterCount.configure({
        limit: maxLength,
      }),
      Placeholder.configure({
        placeholder: placeholder,
      }),
      TextStyle,
      Color,
      Highlight.configure({
        multicolor: true,
      }),
    ],
    content: value || '',
    editable: !disabled,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      const count = editor.storage.characterCount.characters()
      setCharacterCount(count)
      onChange?.(html)
    },
    onBlur: () => {
      onBlur?.()
    },
  })

  // Update editor content when value changes externally
  useEffect(() => {
    if (editor && value !== undefined && editor.getHTML() !== value) {
      editor.commands.setContent(value, false, { preserveWhitespace: 'full' })
    }
  }, [editor, value])

  // Update character count when editor changes
  useEffect(() => {
    if (editor) {
      const count = editor.storage.characterCount.characters()
      setCharacterCount(count)
    }
  }, [editor])

  // Imperative handle for ref methods
  useImperativeHandle(ref, () => ({
    focus: () => {
      editor?.commands.focus()
    },
    blur: () => {
      editor?.commands.blur()
      onBlur?.()
    },
    getEditor: () => editor
  }))

  const isOverLimit = maxLength && characterCount > maxLength

  return (
    <div className={wrapperClassName}>
      <label className="block text-xs font-semibold capitalize not-italic text-txt-title">
        {label}
        {required && (
          <span className="ml-1 inline-block text-xs font-semibold capitalize not-italic text-txt-negative">
            *
          </span>
        )}
      </label>

      <div className="mt-2">
        <div
          className={`tiptap-editor ${error ? 'error' : ''} ${disabled ? 'disabled' : ''} ${isOverLimit ? 'over-limit' : ''}`}
        >
          <EditorContent
            editor={editor}
            className="tiptap-content"
          />
        </div>

        {/* Character count and error info */}
        <div className="mt-1 flex justify-between items-center">
          <div>
            {error && (
              <p className="flex items-center text-xs normal-case text-txt-negative">
                <ReactSVG
                  beforeInjection={(svg) => {
                    svg.classList.add('w-3.5', 'h-3.5', 'fill-txt-negative')
                  }}
                  className="mr-1"
                  src={formInvalidIcon}
                />
                {error}
              </p>
            )}
          </div>
          <span className={`text-xs ${isOverLimit ? 'text-txt-negative' : 'text-txt-inactive'}`}>
            {characterCount}/{maxLength}
          </span>
        </div>
      </div>
    </div>
  )
}

export const AppTiptapEditor = forwardRef(AppTiptapEditorInner)
