/* Tiptap Editor Container Styles */
.tiptap-editor {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #ffffff;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.tiptap-editor:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.tiptap-editor.error {
  border-color: #ef4444;
}

.tiptap-editor.error:focus-within {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.tiptap-editor.disabled {
  background-color: #f9fafb;
  border-color: #d1d5db;
  opacity: 0.6;
  cursor: not-allowed;
}

.tiptap-editor.over-limit {
  border-color: #ef4444;
}

.tiptap-content {
  padding: 12px;
  min-height: 200px;
}

/* Tiptap Editor Content Styles */
.tiptap-content .ProseMirror {
  outline: none;
  border: none;
  background: transparent;
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  min-height: 200px;
}

.tiptap-content .ProseMirror p {
  margin: 8px 0;
  line-height: 1.6;
}

.tiptap-content .ProseMirror p.is-editor-empty:first-child::before {
  color: #9ca3af;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

.tiptap-content .ProseMirror h1,
.tiptap-content .ProseMirror h2,
.tiptap-content .ProseMirror h3,
.tiptap-content .ProseMirror h4,
.tiptap-content .ProseMirror h5,
.tiptap-content .ProseMirror h6 {
  font-weight: 600;
  color: #111827;
  margin: 16px 0 8px 0;
  line-height: 1.3;
}

.tiptap-content .ProseMirror h1 {
  font-size: 24px;
}

.tiptap-content .ProseMirror h2 {
  font-size: 20px;
}

.tiptap-content .ProseMirror h3 {
  font-size: 18px;
}

.tiptap-content .ProseMirror h4 {
  font-size: 16px;
}

.tiptap-content .ProseMirror h5 {
  font-size: 14px;
}

.tiptap-content .ProseMirror h6 {
  font-size: 12px;
}

/* List Styles */
.tiptap-content .ProseMirror ul,
.tiptap-content .ProseMirror ol {
  margin: 8px 0;
  padding-left: 24px;
}

.tiptap-content .ProseMirror li {
  line-height: 1.6;
  margin: 4px 0;
}

.tiptap-content .ProseMirror ul li {
  list-style-type: disc;
}

.tiptap-content .ProseMirror ol li {
  list-style-type: decimal;
}

/* Quote Styles */
.tiptap-content .ProseMirror blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid #e5e7eb;
  background-color: #f9fafb;
  border-radius: 0 4px 4px 0;
  font-style: italic;
  color: #374151;
}

/* Code Block Styles */
.ce-code {
  background-color: #f3f4f6 !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  margin: 8px 0 !important;
}

.ce-code__textarea {
  background: transparent !important;
  border: none !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 13px !important;
  line-height: 1.5 !important;
  color: #374151 !important;
  padding: 12px !important;
  resize: vertical !important;
}

/* Table Styles */
.tc-table {
  border-collapse: collapse !important;
  width: 100% !important;
  margin: 8px 0 !important;
}

.tc-cell {
  border: 1px solid #e5e7eb !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.tc-cell--selected {
  background-color: #eff6ff !important;
}

/* Link Styles */
.cdx-link {
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  padding: 12px !important;
  margin: 8px 0 !important;
  background-color: #f9fafb !important;
}

.cdx-link__title {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #111827 !important;
  margin-bottom: 4px !important;
}

.cdx-link__description {
  font-size: 12px !important;
  color: #6b7280 !important;
  line-height: 1.4 !important;
}

.cdx-link__anchor {
  font-size: 12px !important;
  color: #3b82f6 !important;
  text-decoration: none !important;
}

/* Image Styles */
.cdx-simple-image {
  margin: 16px 0 !important;
}

.cdx-simple-image__picture {
  max-width: 100% !important;
  height: auto !important;
  border-radius: 6px !important;
}

.cdx-simple-image__caption {
  font-size: 12px !important;
  color: #6b7280 !important;
  text-align: center !important;
  margin-top: 8px !important;
  font-style: italic !important;
}

/* Inline Tools */
.ce-inline-tool {
  color: #374151 !important;
}

.ce-inline-tool--active {
  color: #3b82f6 !important;
}

/* Toolbar Styles */
.ce-toolbar__content {
  max-width: none !important;
}

.ce-toolbar__plus {
  color: #6b7280 !important;
}

.ce-toolbar__plus:hover {
  color: #374151 !important;
}

.ce-toolbar__settings-btn {
  color: #6b7280 !important;
}

.ce-toolbar__settings-btn:hover {
  color: #374151 !important;
}

/* Popover Styles */
.ce-popover {
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.ce-popover__item {
  font-size: 14px !important;
  color: #374151 !important;
}

.ce-popover__item:hover {
  background-color: #f3f4f6 !important;
}

.ce-popover__item--active {
  background-color: #eff6ff !important;
  color: #3b82f6 !important;
}

/* Placeholder Styles */
.codex-editor--empty .ce-block:first-child .ce-paragraph[data-placeholder]:empty::before {
  color: #9ca3af !important;
  font-style: italic !important;
}

/* Focus Styles */
.ce-block--focused {
  background-color: rgba(59, 130, 246, 0.05) !important;
  border-radius: 4px !important;
}

/* Selection Styles */
.ce-block--selected {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 4px !important;
}

/* Disabled State */
.editorjs-editor.disabled .codex-editor {
  pointer-events: none !important;
}

.editorjs-editor.disabled .ce-block__content {
  opacity: 0.6 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .editorjs-content {
    padding: 8px;
  }
  
  .ce-paragraph {
    font-size: 13px !important;
  }
  
  .ce-header[data-level="1"] {
    font-size: 20px !important;
  }
  
  .ce-header[data-level="2"] {
    font-size: 18px !important;
  }
  
  .ce-header[data-level="3"] {
    font-size: 16px !important;
  }
}
