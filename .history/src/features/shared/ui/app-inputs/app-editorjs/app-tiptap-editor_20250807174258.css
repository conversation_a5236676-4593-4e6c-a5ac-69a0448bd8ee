/* Editor.js Container Styles */
.editorjs-editor {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #ffffff;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.editorjs-editor:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.editorjs-editor.error {
  border-color: #ef4444;
}

.editorjs-editor.error:focus-within {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.editorjs-editor.disabled {
  background-color: #f9fafb;
  border-color: #d1d5db;
  opacity: 0.6;
  cursor: not-allowed;
}

.editorjs-editor.over-limit {
  border-color: #ef4444;
}

.editorjs-content {
  padding: 12px;
  min-height: 200px;
}

/* Editor.js Core Styles Override */
.codex-editor {
  border: none !important;
  background: transparent !important;
}

.codex-editor__redactor {
  padding: 0 !important;
}

.ce-block__content {
  margin: 0 !important;
  padding: 8px 0 !important;
}

.ce-paragraph {
  line-height: 1.6 !important;
  font-size: 14px !important;
  color: #374151 !important;
}

.ce-header {
  font-weight: 600 !important;
  color: #111827 !important;
  margin: 12px 0 8px 0 !important;
}

.ce-header[data-level="1"] {
  font-size: 24px !important;
  line-height: 1.3 !important;
}

.ce-header[data-level="2"] {
  font-size: 20px !important;
  line-height: 1.4 !important;
}

.ce-header[data-level="3"] {
  font-size: 18px !important;
  line-height: 1.4 !important;
}

.ce-header[data-level="4"] {
  font-size: 16px !important;
  line-height: 1.5 !important;
}

.ce-header[data-level="5"] {
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.ce-header[data-level="6"] {
  font-size: 12px !important;
  line-height: 1.5 !important;
}

/* List Styles */
.cdx-list {
  margin: 8px 0 !important;
}

.cdx-list__item {
  line-height: 1.6 !important;
  font-size: 14px !important;
  color: #374151 !important;
  margin: 4px 0 !important;
}

/* Quote Styles */
.cdx-quote {
  margin: 16px 0 !important;
  padding: 12px 16px !important;
  border-left: 4px solid #e5e7eb !important;
  background-color: #f9fafb !important;
  border-radius: 0 4px 4px 0 !important;
}

.cdx-quote__text {
  font-size: 14px !important;
  line-height: 1.6 !important;
  color: #374151 !important;
  font-style: italic !important;
}

/* Code Block Styles */
.ce-code {
  background-color: #f3f4f6 !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  margin: 8px 0 !important;
}

.ce-code__textarea {
  background: transparent !important;
  border: none !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 13px !important;
  line-height: 1.5 !important;
  color: #374151 !important;
  padding: 12px !important;
  resize: vertical !important;
}

/* Table Styles */
.tc-table {
  border-collapse: collapse !important;
  width: 100% !important;
  margin: 8px 0 !important;
}

.tc-cell {
  border: 1px solid #e5e7eb !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.tc-cell--selected {
  background-color: #eff6ff !important;
}

/* Link Styles */
.cdx-link {
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  padding: 12px !important;
  margin: 8px 0 !important;
  background-color: #f9fafb !important;
}

.cdx-link__title {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #111827 !important;
  margin-bottom: 4px !important;
}

.cdx-link__description {
  font-size: 12px !important;
  color: #6b7280 !important;
  line-height: 1.4 !important;
}

.cdx-link__anchor {
  font-size: 12px !important;
  color: #3b82f6 !important;
  text-decoration: none !important;
}

/* Image Styles */
.cdx-simple-image {
  margin: 16px 0 !important;
}

.cdx-simple-image__picture {
  max-width: 100% !important;
  height: auto !important;
  border-radius: 6px !important;
}

.cdx-simple-image__caption {
  font-size: 12px !important;
  color: #6b7280 !important;
  text-align: center !important;
  margin-top: 8px !important;
  font-style: italic !important;
}

/* Inline Tools */
.ce-inline-tool {
  color: #374151 !important;
}

.ce-inline-tool--active {
  color: #3b82f6 !important;
}

/* Toolbar Styles */
.ce-toolbar__content {
  max-width: none !important;
}

.ce-toolbar__plus {
  color: #6b7280 !important;
}

.ce-toolbar__plus:hover {
  color: #374151 !important;
}

.ce-toolbar__settings-btn {
  color: #6b7280 !important;
}

.ce-toolbar__settings-btn:hover {
  color: #374151 !important;
}

/* Popover Styles */
.ce-popover {
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.ce-popover__item {
  font-size: 14px !important;
  color: #374151 !important;
}

.ce-popover__item:hover {
  background-color: #f3f4f6 !important;
}

.ce-popover__item--active {
  background-color: #eff6ff !important;
  color: #3b82f6 !important;
}

/* Placeholder Styles */
.codex-editor--empty .ce-block:first-child .ce-paragraph[data-placeholder]:empty::before {
  color: #9ca3af !important;
  font-style: italic !important;
}

/* Focus Styles */
.ce-block--focused {
  background-color: rgba(59, 130, 246, 0.05) !important;
  border-radius: 4px !important;
}

/* Selection Styles */
.ce-block--selected {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-radius: 4px !important;
}

/* Disabled State */
.editorjs-editor.disabled .codex-editor {
  pointer-events: none !important;
}

.editorjs-editor.disabled .ce-block__content {
  opacity: 0.6 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .editorjs-content {
    padding: 8px;
  }
  
  .ce-paragraph {
    font-size: 13px !important;
  }
  
  .ce-header[data-level="1"] {
    font-size: 20px !important;
  }
  
  .ce-header[data-level="2"] {
    font-size: 18px !important;
  }
  
  .ce-header[data-level="3"] {
    font-size: 16px !important;
  }
}
